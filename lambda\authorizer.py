import json
import boto3
import logging
import jwt
import os
import time
from typing import Dict, Optional
import base64
import hashlib
import hmac

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
dynamodb = boto3.resource('dynamodb')
secretsmanager = boto3.client('secretsmanager')

# Environment variables
API_KEYS_TABLE = os.environ.get('API_KEYS_TABLE', 'api_keys')
JWT_SECRET_NAME = os.environ.get('JWT_SECRET_NAME', 'notification-platform/jwt-secret')
TOKEN_CACHE_TTL = int(os.environ.get('TOKEN_CACHE_TTL', '300'))  # 5 minutes

# In-memory cache for tokens (Lambda container reuse)
token_cache = {}

def lambda_handler(event, context):
    """
    Lambda Authorizer for API Gateway
    Validates API keys, JWT tokens, and other authentication methods
    """
    
    try:
        # Extract authorization info from event
        auth_token = extract_auth_token(event)
        method_arn = event['methodArn']
        
        if not auth_token:
            logger.warning("No authorization token provided")
            raise Exception('Unauthorized')
        
        # Determine auth type and validate
        if auth_token.startswith('Bearer '):
            # JWT token authentication
            jwt_token = auth_token[7:]  # Remove 'Bearer ' prefix
            principal_id, context_data = validate_jwt_token(jwt_token)
            
        elif auth_token.startswith('ApiKey '):
            # API Key authentication
            api_key = auth_token[7:]  # Remove 'ApiKey ' prefix
            principal_id, context_data = validate_api_key(api_key)
            
        elif len(auth_token) == 64 and auth_token.isalnum():
            # Plain API key (64 char alphanumeric)
            principal_id, context_data = validate_api_key(auth_token)
            
        else:
            logger.warning(f"Invalid auth token format: {auth_token[:10]}...")
            raise Exception('Unauthorized')
        
        # Generate policy allowing access
        policy = generate_policy(principal_id, 'Allow', method_arn, context_data)
        
        logger.info(f"Authentication successful for principal: {principal_id}")
        return policy
        
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        # Return explicit deny policy
        return generate_policy('unauthorized', 'Deny', event['methodArn'])

def extract_auth_token(event: Dict) -> Optional[str]:
    """
    Extract authentication token from various possible locations
    """
    # Check Authorization header
    auth_header = event.get('authorizationToken')
    if auth_header:
        return auth_header
    
    # Check headers in request context (for REQUEST authorizer)
    headers = event.get('headers', {})
    
    # Case-insensitive header lookup
    for key, value in headers.items():
        if key.lower() == 'authorization':
            return value
        elif key.lower() == 'x-api-key':
            return f"ApiKey {value}"
    
    # Check query parameters
    query_params = event.get('queryStringParameters') or {}
    if 'api_key' in query_params:
        return f"ApiKey {query_params['api_key']}"
    
    return None

def validate_jwt_token(token: str) -> tuple[str, Dict]:
    """
    Validate JWT token and return principal ID and context
    """
    try:
        # Check cache first
        cache_key = hashlib.sha256(token.encode()).hexdigest()
        if cache_key in token_cache:
            cached_data = token_cache[cache_key]
            if cached_data['expires'] > time.time():
                return cached_data['principal_id'], cached_data['context']
            else:
                del token_cache[cache_key]
        
        # Get JWT secret from AWS Secrets Manager
        jwt_secret = get_jwt_secret()
        
        # Decode and validate JWT
        payload = jwt.decode(
            token, 
            jwt_secret, 
            algorithms=['HS256'],
            options={
                'verify_exp': True,
                'verify_iat': True,
                'verify_signature': True
            }
        )
        
        # Extract user information
        principal_id = payload.get('sub') or payload.get('user_id')
        if not principal_id:
            raise ValueError("Token missing subject/user_id")
        
        # Prepare context data to pass to backend
        context_data = {
            'user_id': principal_id,
            'email': payload.get('email', ''),
            'role': payload.get('role', 'user'),
            'permissions': payload.get('permissions', []),
            'client_id': payload.get('client_id', ''),
            'token_type': 'jwt'
        }
        
        # Cache valid token
        token_cache[cache_key] = {
            'principal_id': principal_id,
            'context': context_data,
            'expires': time.time() + TOKEN_CACHE_TTL
        }
        
        return principal_id, context_data
        
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token expired")
        raise Exception('Token expired')
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        raise Exception('Invalid token')
    except Exception as e:
        logger.error(f"JWT validation error: {str(e)}")
        raise Exception('Token validation failed')

def validate_api_key(api_key: str) -> tuple[str, Dict]:
    """
    Validate API key against DynamoDB table
    """
    try:
        # Hash API key for lookup (for security)
        api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Check cache first
        if api_key_hash in token_cache:
            cached_data = token_cache[api_key_hash]
            if cached_data['expires'] > time.time():
                return cached_data['principal_id'], cached_data['context']
            else:
                del token_cache[api_key_hash]
        
        # Query DynamoDB for API key
        table = dynamodb.Table(API_KEYS_TABLE)
        response = table.get_item(
            Key={'api_key_hash': api_key_hash}
        )
        
        if 'Item' not in response:
            logger.warning(f"API key not found: {api_key[:8]}...")
            raise Exception('Invalid API key')
        
        api_key_data = response['Item']
        
        # Check if API key is active
        if not api_key_data.get('is_active', False):
            logger.warning(f"Inactive API key: {api_key[:8]}...")
            raise Exception('API key inactive')
        
        # Check expiration
        if 'expires_at' in api_key_data:
            expires_at = int(api_key_data['expires_at'])
            if expires_at < int(time.time()):
                logger.warning(f"Expired API key: {api_key[:8]}...")
                raise Exception('API key expired')
        
        # Check rate limits
        if not check_rate_limit(api_key_data):
            logger.warning(f"Rate limit exceeded for API key: {api_key[:8]}...")
            raise Exception('Rate limit exceeded')
        
        # Prepare context data
        principal_id = api_key_data.get('client_id') or api_key_data.get('user_id')
        context_data = {
            'client_id': principal_id,
            'client_name': api_key_data.get('client_name', ''),
            'tier': api_key_data.get('tier', 'basic'),
            'permissions': api_key_data.get('permissions', []),
            'rate_limit': api_key_data.get('rate_limit', 1000),
            'token_type': 'api_key'
        }
        
        # Cache valid API key
        token_cache[api_key_hash] = {
            'principal_id': principal_id,
            'context': context_data,
            'expires': time.time() + TOKEN_CACHE_TTL
        }
        
        # Update last used timestamp
        update_last_used(api_key_hash)
        
        return principal_id, context_data
        
    except Exception as e:
        if "Invalid API key" in str(e) or "API key" in str(e):
            raise e
        logger.error(f"API key validation error: {str(e)}")
        raise Exception('API key validation failed')

def check_rate_limit(api_key_data: Dict) -> bool:
    """
    Check if API key is within rate limits
    Simple implementation - can be enhanced with Redis for distributed rate limiting
    """
    try:
        rate_limit = api_key_data.get('rate_limit', 1000)  # requests per hour
        window_start = int(time.time() // 3600) * 3600  # Current hour start
        
        # In production, you'd use Redis or DynamoDB with TTL
        # For now, we'll assume it's handled by API Gateway throttling
        # or implement a simple check
        
        return True  # Simplified - always allow for now
        
    except Exception as e:
        logger.error(f"Rate limit check error: {str(e)}")
        return False

def update_last_used(api_key_hash: str):
    """
    Update last used timestamp for API key (async, don't block auth)
    """
    try:
        table = dynamodb.Table(API_KEYS_TABLE)
        table.update_item(
            Key={'api_key_hash': api_key_hash},
            UpdateExpression='SET last_used = :timestamp',
            ExpressionAttributeValues={':timestamp': int(time.time())}
        )
    except Exception as e:
        # Don't fail auth if update fails
        logger.error(f"Failed to update last_used: {str(e)}")

def get_jwt_secret() -> str:
    """
    Get JWT secret from AWS Secrets Manager with caching
    """
    global _jwt_secret_cache
    
    # Simple in-memory cache
    if hasattr(get_jwt_secret, 'cached_secret'):
        if hasattr(get_jwt_secret, 'cache_expires') and get_jwt_secret.cache_expires > time.time():
            return get_jwt_secret.cached_secret
    
    try:
        response = secretsmanager.get_secret_value(SecretId=JWT_SECRET_NAME)
        secret = response['SecretString']
        
        # Cache for 1 hour
        get_jwt_secret.cached_secret = secret
        get_jwt_secret.cache_expires = time.time() + 3600
        
        return secret
        
    except Exception as e:
        logger.error(f"Failed to retrieve JWT secret: {str(e)}")
        raise Exception('Authentication service unavailable')

def generate_policy(principal_id: str, effect: str, resource: str, context: Dict = None) -> Dict:
    """
    Generate IAM policy for API Gateway
    """
    policy = {
        'principalId': principal_id,
        'policyDocument': {
            'Version': '2012-10-17',
            'Statement': [
                {
                    'Action': 'execute-api:Invoke',
                    'Effect': effect,
                    'Resource': resource
                }
            ]
        }
    }
    
    # Add context data to pass to backend
    if context:
        policy['context'] = {
            # Convert all values to strings (API Gateway requirement)
            key: str(value) if not isinstance(value, (str, int, float, bool)) else str(value)
            for key, value in context.items()
        }
    
    return policy

# Lambda Authorizer Response Handler
def create_authorizer_response(principal_id: str, effect: str, method_arn: str, context: Dict = None):
    """
    Create properly formatted authorizer response
    """
    auth_response = {
        'principalId': principal_id
    }
    
    if effect and method_arn:
        auth_response['policyDocument'] = {
            'Version': '2012-10-17',
            'Statement': [
                {
                    'Action': 'execute-api:Invoke',
                    'Effect': effect,
                    'Resource': method_arn
                }
            ]
        }
    
    # Add context to be passed to the backend
    if context:
        auth_response['context'] = {}
        for key, value in context.items():
            # API Gateway context values must be strings, numbers, or booleans
            if isinstance(value, (list, dict)):
                auth_response['context'][key] = json.dumps(value)
            else:
                auth_response['context'][key] = str(value)
    
    return auth_response